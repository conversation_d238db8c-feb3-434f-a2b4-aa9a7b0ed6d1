# 脚本目录结构要求

为确保工具能够正确识别和处理相关文件，项目目录结构需遵循以下规范：

## 一、根目录结构
工具需在项目根目录运行，根目录下必须包含以下关键文件夹：
```c
项目根目录 /
├─ build/ # 构建输出目录
└─ main/ # 主程序源码目录
```


## 二、build目录结构
	build/
	├─ boot/ # boot 工程输出目录
	│ └─ [子目录...] # 可包含任意子目录
	│ └─ *.hex # boot 程序的 hex 文件（必须存在）
	│
	└─ inv/ # 业务逻辑工程输出目录
	└─ [子目录...] # 可包含任意子目录
	└─ *.hex # 业务程序的 hex 文件（必须存在）


## 三、main目录结构
	main/
	└─ bsp/
	└─ stm32/ # STM32 相关文件目录
	├─ [stm32 型号目录]/ # 如 stm32g474、stm32f103 等
	│ └─ obj/ # 编译输出目录
	│ ├─ *.bin # 业务程序的 bin 文件（必须存在）
	│ └─ BIN_ADD_CRC.exe # CRC 校验工具（必须存在）
	│
	└─ libraries/ # STM32 库文件目录
	└─ STM32 [系列] xx_HAL/ # 如 STM32G4xx_HAL
	└─ CMSIS/
	└─ Device/
	└─ ST/
	└─ STM32 [系列] xx/ # 如 STM32G4xx
	└─ Source/
	└─ Templates/
	└─ arm/ # 启动文件目录
	└─ startup_stm32 [型号] xx.s # 启动文件（含版本号定义）


## 四、关键文件路径说明

### 1. boot hex文件
- 位置：`build/boot/`（包括所有子目录）
- 要求：文件名需包含"boot"（不区分大小写），格式为`.hex`

### 2. 业务hex文件
- 位置：`build/inv/`（包括所有子目录）
- 要求：格式为`.hex`，工具会自动选择最新修改的文件

### 3. 业务bin文件
- 位置：`main/bsp/stm32/[stm32型号]/obj/`（包括所有子目录）
- 示例：`main/bsp/stm32/stm32g474/obj/apb.bin`
- 要求：格式为`.bin`，工具会自动选择最新修改的文件

### 4. CRC工具
- 位置：与业务bin文件同目录（`obj/`目录下）
- 文件名：必须为`BIN_ADD_CRC.exe`（大小写敏感）

### 5. 启动文件（含版本号）
- 位置：`main/bsp/stm32/libraries/STM32[系列]xx_HAL/CMSIS/Device/ST/STM32[系列]xx/Source/Templates/arm/`
- 文件名：`startup_stm32[型号]xx.s`（如`startup_stm32g471xx.s`）
- 版本号定义格式：`FIREWARE_VER    EQU     xxxx;`（如`FIREWARE_VER    EQU     10002;`）

## 五、灵活性说明

- 允许在规定的父目录下创建任意子目录，工具会自动递归查找文件
- STM32型号目录和库目录支持任意系列（如G4、F1、F4、H7等）
- 文件名除关键标识（如"boot"）外，其余部分可自定义

若项目目录结构与上述规范不符，可能导致工具无法找到所需文件，建议在使用前检查并调整目录结构。