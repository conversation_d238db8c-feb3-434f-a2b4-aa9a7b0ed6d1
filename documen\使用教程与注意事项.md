# 《脚本使用教程与注意事项》

由于我无法直接生成 PDF 或 Word 文件，但可以为您提供内容的 Markdown 格式，您可以将其复制到 Word 中，再通过 Word 导出为 PDF。以下是完整内容：


嵌入式固件自动处理工具使用教程与注意事项



一、工具简介



本工具用于自动化处理嵌入式固件，主要功能包括：




*   查找项目中的 boot 文件、业务 hex 文件和 bin 文件


*   从 STM32 启动文件中自动提取版本号


*   对 bin 文件添加 CRC 校验


*   合并 boot 和业务 hex 文件


*   生成规范命名的输出文件


二、环境要求





1.  **操作系统**：Windows 系统（因依赖 Windows 可执行文件 BIN\_ADD\_CRC.exe）


2.  **Python 版本**：Python 3.6 及以上


3.  **项目目录结构**：需符合特定的文件夹组织方式（见下方说明）


三、使用步骤



### 1. 准备工作&#xA;



*   将工具脚本（`firmware_release_tool.py`）放置在项目根目录下


*   确保项目目录结构完整，特别是：



    *   `build/boot`目录下存在 boot hex 文件


    *   `build/inv`目录下存在业务 hex 文件


    *   `main/bsp/stm32`目录下存在 STM32 型号相关目录及 bin 文件


    *   CRC 工具（`BIN_ADD_CRC.exe`）与 bin 文件在同一目录


### 2. 运行工具&#xA;



1.  打开命令提示符（CMD）或 PowerShell


2.  导航到项目根目录：




```
cd 项目根目录路径
```



1.  运行脚本：




```
python firmware\_release\_tool.py
```

### 3. 交互操作&#xA;



*   工具会自动查找所需文件并尝试从启动文件提取版本号


*   若成功提取版本号，无需手动输入


*   若提取失败，工具会提示您手动输入版本号


*   最后需要输入项目名称，用于生成输出文件


### 4. 查看结果&#xA;

处理完成后，工具会在`build`目录下生成类似`burnprocess_py_YYYYMMDD`的输出目录，包含：




*   带 CRC 校验的 bin 文件（命名格式：`项目名称_V版本号.bin`）


*   合并后的 hex 文件（命名格式：`项目名称_V版本号.hex`）


四、注意事项



### 1. 目录结构要求&#xA;



*   启动文件需位于以下类似路径：




```
main/bsp/stm32/libraries/STM32xxxx/CMSIS/Device/ST/STM32xxxx/Source/Templates/arm
```



*   版本号需在启动文件中以`FIREWARE_VER    EQU     xxxx`格式定义


*   STM32 型号目录名称需以`stm32`开头（如`stm32g474`）


### 2. CRC 工具相关&#xA;



*   确保`BIN_ADD_CRC.exe`与 bin 文件在同一目录


*   若 CRC 工具执行失败，会显示详细的输出和错误信息


*   工具会处理已包含`_crc`后缀的文件，避免重复添加


### 3. 版本号处理&#xA;



*   自动提取失败时，需手动输入版本号（如`10014`代表`1.00.14`）


*   版本号格式支持 5 位数字（如 10014→1.00.14）和 4 位数字（如 1014→1.0.14）


### 4. 错误处理&#xA;



*   若工具提示文件未找到，请检查对应目录是否存在所需文件


*   若启动文件目录未找到，可能是目录结构与预期不符，需检查项目结构


*   运行过程中出现错误时，工具会显示错误信息并退出


五、常见问题解决





1.  **"未找到 CRC 工具" 错误**

*   检查`BIN_ADD_CRC.exe`是否存在于 bin 文件所在目录


*   确认 bin 文件路径正确


1.  **"未能从启动文件中提取版本号" 警告**

*   检查启动文件中是否有`FIREWARE_VER`定义


*   确认版本号定义格式是否正确（如`FIREWARE_VER    EQU     10002`）


*   手动输入版本号继续操作


1.  **文件查找失败**

*   检查项目目录结构是否符合要求


*   确认相关文件是否存在于预期目录中


*   检查文件名是否符合常规命名规范


1.  **合并 hex 文件失败**

*   检查 boot 文件和业务文件是否为有效的 hex 格式


*   确认 boot 文件末尾有正确的结束符（`:00000001FF`）


六、使用建议





1.  定期备份重要固件文件


2.  运行工具前确保所有文件已保存且无编译错误


3.  输出文件按日期组织，便于版本追溯


4.  若项目结构有变动，可能需要调整工具中的路径设置



