# BIN_HEX_PY.py 脚本使用说明

## 概述
`BIN_HEX_PY.py` 是一个基于XML配置的固件处理工具，主要用于STM32固件的合并、CRC校验添加和发布文件生成。

## 主要功能
1. **合并HEX文件** - 将Bootloader和Main应用HEX文件合并为单个HEX文件
2. **添加CRC校验** - 使用BIN_ADD_CRC.exe工具为BIN文件添加CRC校验
3. **自动命名** - 根据版本号、日期和项目名称自动生成输出文件名
4. **多目标支持** - 支持多个Keil工程目标分别生成发布文件
5. **智能路径处理** - 自动查找和验证所需文件

## 环境要求
- Python 3.6+
- Windows系统（需要BIN_ADD_CRC.exe工具）
- Keil工程文件（.uvoptx和.uvprojx）

### 1. 准备工作&#xA;

*   将工具脚本（`BIN_ADD_CRC4.exe`）放置在项目根目录下


*   确保项目目录结构完整，特别是：

    *   `build/boot`目录下存在 boot hex 文件或配置文件指定路径存在目标boot.hex

    *   `main_hex`目录下存在业务 hex 文件

    *   `main/bsp/stm32`（）目录下存在 STM32 型号相关目录

    *   CRC 工具（`BIN_ADD_CRC.exe`）与 bin 文件在`main_bin`目录下
    

## 配置文件
脚本使用XML格式的配置文件，默认为 `BIN_HEX_CONFIG.xml`。

### 配置文件结构
```xml
<configuration>
    <!-- 文件路径配置 -->
    <paths>
        <bootloader_hex>路径/boot.hex</bootloader_hex>      <!-- Bootloader HEX文件 -->
        <main_hex>路径/</main_hex>                          <!-- Main HEX文件目录 -->
        <main_bin>路径/</main_bin>                          <!-- Main BIN文件目录 -->
        <output_dir>输出目录/</output_dir>                  <!-- 输出目录 -->
        <startup_file>启动文件.s</startup_file>             <!-- 启动文件（提取版本号） -->
        <keil_uvoptx>工程.uvoptx</keil_uvoptx>              <!-- Keil工程文件 -->
        <keil_uvprojx>工程.uvprojx</keil_uvprojx>           <!-- Keil工程文件 -->
    </paths>
    
    <!-- 设置信息 -->
    <settings>
        <stm32_model>stm32f103</stm32_model>                <!-- STM32型号 -->
        <project_name>项目名</project_name>                 <!-- 项目名称（可选） -->
        <version>10014</version>                            <!-- 版本号（可选） -->
        <output_prefix>burnprocess_py</output_prefix>       <!-- 输出前缀 -->
    </settings>
    
    <!-- 工程发布配置 -->
    <project_releases>
        <project>
            <target_name>PVPB_PFC</target_name>              <!-- Keil目标名称 -->
            <release_hex_name>PV_PB_PFC</release_hex_name>   <!-- 发布HEX文件名 -->
            <release_bin_name>PV_PB_PFC</release_bin_name>   <!-- 发布BIN文件名 -->
        </project>
        <project>
            <target_name>另一个目标</target_name>
            <release_hex_name>另一个文件名</release_hex_name>
            <release_bin_name>另一个文件名</release_bin_name>
        </project>
    </project_releases>
</configuration>
```

## 使用方法

### 基本用法
```bash
python BIN_HEX_PY.py
```

### 使用指定配置文件
```bash
python BIN_HEX_PY.py -c 配置文件.xml
python BIN_HEX_PY.py --config 配置文件.xml
```

## 工作流程

1. **加载配置** - 读取XML配置文件
2. **验证文件** - 检查所有必要的文件是否存在
3. **提取信息** - 从Keil工程文件中提取项目名称和目标
4. **版本处理** - 从启动文件或配置中获取版本号
5. **CRC处理** - 为BIN文件添加CRC校验
6. **合并文件** - 合并Bootloader和Main HEX文件
7. **生成发布文件** - 根据当前目标生成发布文件

## 输出文件

### 标准输出文件
- **合并HEX文件**: `{项目名}_{日期}_V{版本}.hex`
- **CRC BIN文件**: `{项目名}_{日期}_V{版本}.bin`

### 发布文件（可选）
- **发布HEX文件**: `{发布名}_V{主版本}-{次版本}_BSM.hex`
- **发布BIN文件**: `{发布名}_V{主版本}-{次版本}_BSM.bin`

### 输出示例
```
burnprocess_py_20250728/
├── 项目名_20250728_V1.00.14.hex    # 合并后的HEX文件
├── 项目名_20250728_V1.00.14.bin    # 添加CRC后的BIN文件
├── PV_PB_PFC_V1-00_BSM.hex         # 发布HEX文件（PVPB_PFC目标）
└── PV_PB_PFC_V1-00_BSM.bin         # 发布BIN文件（PVPB_PFC目标）
```

## 版本号格式

### 输入格式
- 5位数字：`10014` → 转换为 `V1.00.14`
- 其他格式：保持原样

### 发布文件版本格式
- 发布文件版本格式：`V{主版本}-{次版本}`
- 例如：`10014` → `V1-00`

## 路径处理

### 相对路径
配置中的路径可以是相对路径（相对于脚本所在目录）或绝对路径。

### 路径美化
脚本会自动美化路径显示，显示为相对于项目根目录的路径，例如：
- 原始路径：`C:\Projects\STM32\Project\Build\main.hex`
- 美化显示：`C:\...\Build\main.hex`

## 依赖工具

### BIN_ADD_CRC.exe
- **功能**: 为BIN文件添加CRC校验
- **查找顺序**: 
  1. BIN文件所在目录
  2. 脚本当前目录
- **要求**: 必须与脚本兼容的CRC工具

## 打包exe

- 默认不使用python 命令
```bash
python BIN_HEX_PY.py
```
- 建议打包成 exe格式使用，利于封装
```bash
pyinstaller  BIN_HEX_PY.spec
```
可生成.exe文件在 ./dist目录下




## 错误处理

### 常见错误及解决方法

1. **配置文件不存在**
   - 确保 `BIN_HEX_CONFIG.xml` 文件存在
   - 使用 `-c` 参数指定正确的配置文件

2. **文件路径错误**
   - 检查配置文件中所有路径是否正确
   - 确保所有引用的文件都存在

3. **Keil工程文件读取失败**
   - 确保 `.uvoptx` 和 `.uvprojx` 文件存在
   - 检查文件格式是否正确

4. **BIN_ADD_CRC.exe 未找到**
   - 将工具放在BIN文件目录或脚本目录
   - 检查工具名称是否为 `BIN_ADD_CRC.exe`

5. **版本号提取失败**
   - 确保启动文件中包含版本号定义
   - 手动输入版本号

## 调试建议

### 日志输出
脚本会输出详细的处理信息，包括：
- 使用的配置文件
- 加载的文件路径
- 提取的项目名称和版本
- 生成的文件列表

### 调试步骤
1. 检查配置文件是否正确
2. 验证所有文件路径
3. 确认Keil工程文件格式
4. 检查BIN_ADD_CRC.exe工具位置

## 使用示例

### 场景1：基本使用
```bash
python BIN_HEX_PY.py
```

### 场景2：多个项目
为不同项目创建不同的配置文件：
```bash
python BIN_HEX_PY.py -c config_pfc.xml
python BIN_HEX_PY.py -c config_inverter.xml
```

### 场景3：CI/CD集成
在自动化构建中使用：
```bash
python BIN_HEX_PY.py -c build_config.xml
```

### 场景4：keil配置
如图在`Options../User`选项卡
`RUN #2`下增加
```bash
BIN_ADD_CRC4 -c BIN_HEX_CONFIG1.xml`
BIN_ADD_CRC4 -c BIN_HEX_CONFIG2.xml`
```

![alt text](image.png)

## 注意事项

1. **文件编码** - 确保XML配置文件使用UTF-8编码
2. **路径分隔符** - Windows系统使用反斜杠 `\` 或正斜杠 `/`
3. **版本号** - 建议使用5位数字格式（如10014）
4. **日期格式** - 输出目录使用当前日期（YYYYMMDD格式）
5. **权限** - 确保有读写相关目录的权限

