@echo off
chcp 65001 >nul
echo ========================================
echo 正在打包 BIN_HEX_PY.py 为 exe 文件
echo ========================================

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

:: 检查PyInstaller是否安装
pip show pyinstaller >nul 2>&1
if errorlevel 1 (
    echo 正在安装PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo 错误: PyInstaller安装失败
        pause
        exit /b 1
    )
)

:: 检查必要文件是否存在
if not exist "BIN_HEX_PY.py" (
    echo 错误: 未找到 BIN_HEX_PY.py 文件
    pause
    exit /b 1
)

:: 清理之前的构建文件
echo 清理之前的构建文件...
if exist "dist" rmdir /s /q "dist"
if exist "__pycache__" rmdir /s /q "__pycache__"

:: 使用PyInstaller打包
echo 开始打包...
pyinstaller BIN_HEX_PY.spec

if errorlevel 1 (
    echo 错误: 打包失败
    pause
    exit /b 1
)

:: 检查生成的exe文件
if exist "dist\BIN_HEX_PY.exe" (
    echo ========================================
    echo 打包成功！
    echo 生成的exe文件位置: dist\BIN_HEX_PY.exe
    echo ========================================
    
    :: 复制必要的配置文件到dist目录
    echo 复制配置文件到输出目录...
    copy "BIN_HEX_CONFIG.xml" "dist\" >nul
    copy "BIN_ADD_CRC.exe" "dist\" >nul

    echo 配置文件已复制到 dist 目录
    echo.
    echo 使用方法:
    echo   1. 将 dist 目录中的所有文件复制到目标位置
    echo   2. 运行 BIN_HEX_PY.exe
    echo   3. 或使用命令行: BIN_HEX_PY.exe -c config_file.xml
    echo.
    
    :: 询问是否打开输出目录
    set /p choice="是否打开输出目录? (y/n): "
    if /i "%choice%"=="y" (
        explorer "dist"
    )
) else (
    echo 错误: 未找到生成的exe文件
    pause
    exit /b 1
)

pause
